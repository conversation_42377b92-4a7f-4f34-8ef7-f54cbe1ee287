package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	log "gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"golang.org/x/crypto/bcrypt"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLEmployeeRepository struct {
	mysql.Repository
}

// NewMySQLEmployeeRepository func
func NewMySQLEmployeeRepository(conn *sql.DB) domain.EmployeeRepository {
	return &mySQLEmployeeRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLEmployeeRepository) Fetch(adminFkid int) ([]domain.Employee, error) {
	// Use FetchWithFilter with default hr_only filter for backward compatibility
	filter := domain.EmployeeFilter{HRRegistered: "hr_only"}
	return m.FetchWithFilter(adminFkid, filter)
}

func (m *mySQLEmployeeRepository) FetchWithFilter(adminFkid int, filter domain.EmployeeFilter) ([]domain.Employee, error) {
	// Determine query structure based on hr_registered filter
	var query string

	// Query for all employees (including those not in hrm_employee)
	query = `SELECT DISTINCT
			COALESCE(hem.hrm_employee_id, 0) AS HEMP_ID,
			COALESCE(hem.nik, '') AS NIK,
			COALESCE(hem.hrm_employee_id, 0) AS ID,
			COALESCE(hem.employee_sallary, 0) AS SALLARY,
			COALESCE(hem.type_fkid, 0) AS TYPE_FKID,
			COALESCE(hem.max_leave, 0) AS MAX_LEAVE,
			hem.name AS NAME,
			COALESCE(typ.type_name, '') AS TYPE_NAME,
			hem.date_join,
			hem.jabatan_fkid,
			COALESCE(ej.name, '') AS jabatan_name
			FROM hrm_employee AS hem 
			JOIN hrm_employee_type AS typ ON typ.type_id = hem.type_fkid
			JOIN employees_jabatan AS ej ON ej.jabatan_id = hem.jabatan_fkid`

	// Query for HR registered employees only
	// query = `SELECT DISTINCT hem.hrm_employee_id AS HEMP_ID, hem.nik AS NIK, hem.hrm_employee_id AS ID,
	// 	 hem.employee_sallary AS SALLARY, hem.employee_fkid AS HEMP_FKID, hem.type_fkid AS TYPE_FKID,
	// 	 hem.max_leave AS MAX_LEAVE, emp.name AS NAME, emp.employee_id AS EMP_ID, typ.type_name AS TYPE_NAME,
	// 	 emp.date_join, emp.jabatan_fkid, ej.name AS jabatan_name
	// 	 FROM hrm_employee AS hem RIGHT
	// 	 JOIN employee AS emp ON emp.employee_id = hem.employee_fkid
	// 	 LEFT JOIN hrm_employee_type AS typ ON typ.type_id = hem.type_fkid
	// 	 LEFT JOIN employees_jabatan AS ej ON ej.jabatan_id = emp.jabatan_fkid`

	// Add outlet filter if needed
	// if len(filter.OutletIDs) > 0 {
	// 	query += ` JOIN employee_outlet AS eo ON emp.employee_id = eo.employee_fkid`
	// }

	query += ` WHERE hem.admin_fkid = @adminId AND hem.data_status = 'on'`

	// Add outlet filter
	if len(filter.OutletIDs) > 0 {
		query += ` AND hem.outlet_fkid IN @outletIds `
	}

	// Add employee type filter (only applicable for HR registered employees)
	if len(filter.EmployeeTypeIDs) > 0 {
		query += ` AND hem.type_fkid IN @employeeTypeIds `
	}

	// Add jabatan filter
	if len(filter.JabatanIDs) > 0 {
		query += ` AND hem.jabatan_fkid IN @jabatanIds `
	}

	query, params := mysql.MapParam(query, map[string]any{
		"adminId":         adminFkid,
		"outletIds":       filter.OutletIDs,
		"employeeTypeIds": filter.EmployeeTypeIDs,
		"jabatanIds":      filter.JabatanIDs,
	})

	// Add appropriate ORDER BY clause based on hr_registered filter
	// if hrRegistered == "all" {
	// 	query += ` ORDER BY emp.employee_id DESC`
	// } else {
	// 	query += ` ORDER BY hrm_employee_id DESC`
	// }

	var result []domain.Employee

	err := m.Query(query, params...).PrintSql().Model(&result)
	if err != nil {
		log.Info(">>> using model query err, use old query")

		// Execute query
		results, err := m.QueryArrayOld(query, params...)
		if err != nil {
			log.IfError(err)
			return nil, err
		}

		resultJSON, err := json.Marshal(results)
		if err != nil {
			log.IfError(err)
			return nil, err
		}
		log.IfError(json.Unmarshal(resultJSON, &result))

	}

	for k := range result {
		result[k].NO = k + 1
	}
	return result, err
}

func (m *mySQLEmployeeRepository) FetchSingle(hrmID int, employeeID int) (domain.EmployeeDetail, error) {
	var result domain.EmployeeDetail

	// Join query to get data from both hrm_employee and employee tables
	query := `
		SELECT
	he.hrm_employee_id,
	he.admin_fkid,
	he.profile_img,
	he.data_status,
	he.nik,
	he.type_fkid,
	he.max_leave,
	he.employee_sallary,
	he.data_created AS hrm_data_created,
	he.data_modified AS hrm_data_modified,
	he.npwp,
	he.name,
	he.address,
	he.phone,
	he.photo,
	he.last_login,
	he.jabatan_fkid,
	he.email,
	he.date_join,
	he.data_created AS emp_data_created,
	he.data_modified AS emp_data_modified,
	employees_jabatan.name AS employees_jabatan_name,
	hrm_employee_type.type_name AS hrm_employee_type_name
FROM	
	hrm_employee he
	JOIN employees_jabatan ON he.jabatan_fkid = employees_jabatan.jabatan_id
	LEFT JOIN hrm_employee_type ON he.type_fkid = hrm_employee_type.type_id
WHERE`

	var args []any

	// If hrmID is provided and not 0, use hrm_employee_id
	if hrmID > 0 {
		query += ` he.hrm_employee_id = ?`
		args = append(args, hrmID)
	} else if employeeID > 0 {
		// If employeeID is provided, use employee_id
		query += ` he.employee_fkid = ?`
		args = append(args, employeeID)
	} else {
		return domain.EmployeeDetail{}, fmt.Errorf("either hrmID or employeeID must be provided")
	}

	err := m.Query(query, args...).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.EmployeeDetail{}, err
	}

	return result, nil
}

func (m *mySQLEmployeeRepository) FetchAddInfo(hrmIDs ...int) ([]domain.HrmAddInfo, error) {
	if len(hrmIDs) == 0 {
		return []domain.HrmAddInfo{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(hrmIDs))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	// Build query
	query := fmt.Sprintf("SELECT * FROM hrm_additional_employee_info WHERE hrm_employee_fkid IN (%s)", placeholders)

	// Convert hrmIDs to []any for QueryArrayOld
	args := make([]any, len(hrmIDs))
	for i, id := range hrmIDs {
		args[i] = id
	}

	// Execute query
	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	// Convert to JSON and then to struct
	resultJSON, err := json.Marshal(results)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var addInfo []domain.HrmAddInfo
	err = json.Unmarshal(resultJSON, &addInfo)
	return addInfo, err
}

func (m *mySQLEmployeeRepository) AddEmployee(employee domain.EmployeeData) (int64, error) {
	var hrmEmployeeID int64
	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Prepare data for hrm_employee table
		hrmEmployeeTableMap := map[string]any{
			"nik":              employee.Nik,
			"type_fkid":        employee.TypeFkid,
			"max_leave":        employee.MaxLeave,
			"employee_sallary": employee.EmployeeSallary,
			"npwp":             employee.Npwp,
			"data_modified":    time.Now().UnixMilli(),
			"name":             employee.Name,
			"date_join":        employee.JoinDate,
			"jabatan_fkid":     employee.JabatanId,
			"email":            employee.Email,
			"phone":            employee.Phone,
			"address":          employee.Address,
		}

		if outletIDs := strings.Split(employee.OutletIds, ","); len(outletIDs) > 0 {
			hrmEmployeeTableMap["outlet_fkid"] = outletIDs[0]
		}

		// Check if we're updating or inserting
		if employee.HrmEmployeeID != "" {
			log.Info("Updating employee, hrm_employee_id: %v", employee.HrmEmployeeID)
			// Update existing employee
			hrmEmployeeIDInt, _ := strconv.Atoi(employee.HrmEmployeeID)

			// First, get the employee_fkid from hrm_employee
			hrmEmployee, err := m.Query("SELECT employee_fkid FROM hrm_employee WHERE hrm_employee_id = ? limit 1", hrmEmployeeIDInt).Map()
			if log.IfError(err) {
				return err
			}

			if len(hrmEmployee) == 0 {
				return fmt.Errorf("employee not found")
			}

			// Update hrm_employee table
			tx.Update("hrm_employee", hrmEmployeeTableMap, "hrm_employee_id =?", hrmEmployeeIDInt)
			hrmEmployeeID = int64(hrmEmployeeIDInt)
		} else {
			//before add new account, check if data exist (with the same email)
			accountMap, err := m.Query("SELECT id FROM accounts WHERE email = ? limit 1", employee.Email).Map()
			if log.IfError(err) {
				return err
			}

			accountID := cast.ToInt64(accountMap["id"])

			if accountID == 0 {
				// Prepare account data
				accountData := map[string]any{
					"name":  employee.Name,
					"email": employee.Email,
					"phone": employee.Phone,
					// "password":   string(hashedPassword),
					"created_at": time.Now().Unix(),
					"updated_at": time.Now().Unix(),
					"is_active":  1,
				}

				// Insert into accounts table
				accountResult := tx.Insert("accounts", accountData)
				accountID, err = accountResult.LastInsertId()
				if err != nil {
					log.Info("Error getting account ID: %v", err)
					return err
				}
				log.Info("inserted account: %v", accountID)
			}

			// Add employee_fkid to hrm_employee data
			hrmEmployeeTableMap["account_id"] = accountID
			hrmEmployeeTableMap["data_created"] = time.Now().UnixMilli()

			if outletIDs := strings.Split(employee.OutletIds, ","); len(outletIDs) > 0 {
				hrmEmployeeTableMap["outlet_fkid"] = outletIDs[0]
			}

			// Insert into hrm_employee
			hrmEmployeeResult := tx.Insert("hrm_employee", hrmEmployeeTableMap)
			hrmEmployeeID, _ := hrmEmployeeResult.LastInsertId()
			// _, hrmEmployeeID, _ = m.InsertGetLastID("hrm_employee", hrmEmployeeTableMap)
			log.Info("inserted hrm_employee: %v", hrmEmployeeID)
		}
		return nil
	})

	return hrmEmployeeID, err
}

func (m *mySQLEmployeeRepository) GetAddInfo(empID int) ([]domain.HrmAddInfo, error) {
	info, err := m.QueryArrayOld("SELECT * FROM hrm_additional_employee_info WHERE hrm_employee_fkid=?", empID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resultJSON, err := json.Marshal(info)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var result []domain.HrmAddInfo
	err = json.Unmarshal(resultJSON, &result)
	return result, err
}

func (m *mySQLEmployeeRepository) AddInfo(addInfo []map[string]any) error {
	if len(addInfo) == 0 {
		log.Info("no data to insert")
		return nil
	}
	_, err := m.BulkInsert("hrm_additional_employee_info", addInfo)
	return err
}

func (m *mySQLEmployeeRepository) UpdateInfo(updateInfo []map[string]any) error {
	if len(updateInfo) == 0 {
		log.Info("no data to update")
		return nil
	}
	err := m.BulkUpdate("hrm_additional_employee_info", "add_id", updateInfo)
	return err
}

func (m *mySQLEmployeeRepository) DeleteAttach(where map[string]any) error {
	_, err := m.Deletes("hrm_additional_employee_info", where)
	return err
}

func (m *mySQLEmployeeRepository) FetchEmpAttach(addID int) ([]domain.EmployeeAttach, error) {
	query := "SELECT attachment FROM hrm_additional_employee_info WHERE add_id=?"
	res, err := m.QueryArrayOld(query, addID)
	if err != nil {
		fmt.Printf("query fetch employee atachment error: %v", err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		fmt.Printf("marshalling error: %v", err)
		return nil, err
	}
	var results []domain.EmployeeAttach
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLEmployeeRepository) FetchVectorImg(empID int) ([]domain.VectorImg, error) {
	query := "SELECT img_vector FROM hrm_employee WHERE employee_fkid=?"
	res, err := m.QueryArrayOld(query, empID)
	if err != nil {
		fmt.Printf("query error: %v", err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		fmt.Printf("marshalling response error: %v", err)
		return nil, err
	}
	var results []domain.VectorImg
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (m *mySQLEmployeeRepository) UpdatePofilePhoto(empID int, img string, vector any) error {

	data := map[string]any{
		"img_vector":  vector,
		"profile_img": img,
	}
	where := map[string]any{
		"employee_fkid": empID,
	}
	_, err := m.Updates("hrm_employee", data, where)
	if err != nil {
		fmt.Printf("error updating vector: %v", err)
		return err
	}
	return nil
}

func (m *mySQLEmployeeRepository) FetchEmpImg(empID int) (domain.ProfileImage, error) {
	query := "SELECT * FROM hrm_employee WHERE employee_fkid=?"
	var result domain.ProfileImage
	err := m.Query(query, empID).Model(&result)
	if err != nil {
		fmt.Printf("query employee profile image error: %v", err)
		return domain.ProfileImage{}, err
	}
	return result, err
}

func (m *mySQLEmployeeRepository) GetImageVector(file1 multipart.File, file2 multipart.File, fileName string, empID string) error {
	return nil
}

func (m *mySQLEmployeeRepository) ChangePassword(email string, oldPass string, newPass string) error {
	data := map[string]any{
		"password": newPass,
	}
	where := map[string]any{
		"email": email,
	}
	// update password in emplyee table
	_, err := m.Updates("employee", data, where)
	if err != nil {
		log.IfError(err)
		// update password in accounts table
		_, err := m.Updates("accounts", data, where)
		if err != nil {
			log.IfError(err)
			return err
		}
		loc, _ := time.LoadLocation("Asia/Jakarta")
		subject := "Kata sandi telah di ganti"
		randString := cast.RandStringBytes(32)
		now := time.Now().In(loc).Format("2006-01-02 15:04:05")
		emailLink := url.QueryEscape(email)
		urls := os.Getenv("EMAIL_URL") + `/confirmation/resetpass/` + randString + `/` + emailLink
		// email contents
		content := `<html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>noreply</title></head><body><div><br>============ Forwarded Message ============<br>From : <a href="mailto:noreply.uniq.id" target="_blank"><EMAIL></a><br>To : <a href="mailto:` + email + `">` + email + `</a><br>Date: ` + now + `<br>Subject: ` + subject + `<br>============ Forwarded Message ============<br><br><blockquote style="border-left:1px solid rgb(204,204,204);padding-left:6px;margin-left:5px"><div>Seseorang atau Anda telah melakukan pergantian kata sandi / <i>password</i>. Bila Anda tidak melakukan hal tersebut, silahkan klik link di bawah ini untuk melakukan pergantian password.<br>Link dibawah ini berlaku selama 3 hari sejak email ini dikirim. <br><br><h3><a href="` + urls + `" target="_blank">Klik Disini untuk Mengatur Ulang Password</a></h3><br><br>Atas perhatian Anda, kami ucapkan terima kasih. <br>UNIQ.</div></blockquote></div></body></html>`

		// data post values
		data := url.Values{}
		data.Set("email", email)
		data.Set("subject", subject)
		data.Set("content", content)

		req, err := http.NewRequest("POST", os.Getenv("MESSENGER_URL"), strings.NewReader(data.Encode()))
		req.Header.Set("Authorization", os.Getenv("MESSENGER_TOKEN"))
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		if err != nil {
			log.IfError(err)
			return err
		}
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			fmt.Printf("error send email change password: \n %v", err)
			log.IfError(err)
			return err
		}

		defer resp.Body.Close()

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			log.IfError(err)
		}
		fmt.Println(string(body))
		strBcrypt, err := bcrypt.GenerateFromPassword([]byte(randString), 12)
		if err != nil {
			log.IfError(err)
		}

		err = m.InsertUserKey(email, string(strBcrypt))
		if err != nil {
			log.IfError(err)
			return err
		}
		return nil
	}
	loc, _ := time.LoadLocation("Asia/Jakarta")
	subject := "Kata sandi telah di ganti"
	randString := cast.RandStringBytes(32)
	now := time.Now().In(loc).Format("2006-01-02 15:04:05")
	emailLink := url.QueryEscape(email)
	urls := os.Getenv("EMAIL_URL") + `confirmation/resetpass/` + randString + `/` + emailLink
	content := `<html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>noreply</title></head><body><div><br>============ Forwarded Message ============<br>From : <a href="mailto:noreply.uniq.id" target="_blank"><EMAIL></a><br>To : <a href="mailto:` + email + `">` + email + `</a><br>Date: ` + now + `<br>Subject: ` + subject + `<br>============ Forwarded Message ============<br><br><blockquote style="border-left:1px solid rgb(204,204,204);padding-left:6px;margin-left:5px"><div>Seseorang atau Anda telah melakukan pergantian kata sandi / <i>password</i>. Bila Anda tidak melakukan hal tersebut, silahkan klik link di bawah ini untuk melakukan pergantian password.<br>Link dibawah ini berlaku selama 3 hari sejak email ini dikirim. <br><br><h3><a href="` + urls + `" target="_blank">Klik Disini untuk Mengatur Ulang Password</a></h3><br><br>Atas perhatian Anda, kami ucapkan terima kasih. <br>UNIQ.</div></blockquote></div></body></html>`

	datas := url.Values{}
	datas.Set("email", email)
	datas.Set("subject", subject)
	datas.Set("content", content)

	req, err := http.NewRequest("POST", os.Getenv("MESSENGER_URL"), strings.NewReader(datas.Encode()))
	req.Header.Set("Authorization", os.Getenv("MESSENGER_TOKEN"))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	if err != nil {
		log.IfError(err)
		return err
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.IfError(err)
		return err
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.IfError(err)
	}
	fmt.Println(string(body))
	strBcrypt, err := bcrypt.GenerateFromPassword([]byte(randString), 12)
	if err != nil {
		log.IfError(err)
	}

	err = m.InsertUserKey(email, string(strBcrypt))
	if err != nil {
		log.IfError(err)
		return err
	}
	return err
}

func (m *mySQLEmployeeRepository) FetchEmailNPassword(email string) (domain.EmailNPassword, error) {
	var result domain.EmailNPassword
	query := "SELECT email, password FROM employee WHERE email=?"
	err := m.Query(query, email).Model(&result)
	if err != nil {
		log.IfError(err)
		return domain.EmailNPassword{}, nil
	}

	return result, err
}

func (m *mySQLEmployeeRepository) InsertUserKey(email string, BcryptStr string) error {
	data := map[string]any{
		"key_type":     "forgot",
		"user_level":   "employee",
		"email":        email,
		"secret_key":   BcryptStr,
		"data_created": cast.MakeTimestamp(),
		"data_expired": cast.MakeTimestampAdd(),
	}

	res, err := m.Insert("users_key", data)
	if err != nil {
		log.IfError(err)
	}
	fmt.Printf("inserted users_key: %v \n", res)
	return err
}

func (m *mySQLEmployeeRepository) FetchEmployeeOutlet(hrmEmployeeId int) ([]domain.EmployeeOutlet, error) {
	// query := "SELECT employee_fkid, outlet_fkid, name as outlet_name FROM employee_outlet JOIN outlets ON employee_outlet.outlet_fkid=outlets.outlet_id WHERE employee_fkid=?"
	query := `SELECT he.hrm_employee_id, he.outlet_fkid, o.name AS outlet_name
	FROM hrm_employee he
	JOIN outlets o ON he.outlet_fkid=o.outlet_id
	WHERE he.hrm_employee_id=?`

	var results []domain.EmployeeOutlet
	err := m.Query(query, hrmEmployeeId).Model(&results)
	return results, err
}

// Excel import related methods

func (m *mySQLEmployeeRepository) FindEmployeeByNIK(nik string, adminID int) (*domain.Employee, error) {
	query := `SELECT DISTINCT
		COALESCE(hem.hrm_employee_id, 0) AS HEMP_ID,
		COALESCE(hem.nik, '') AS NIK,
		COALESCE(hem.hrm_employee_id, 0) AS ID,
		COALESCE(hem.employee_sallary, 0) AS SALLARY,
		COALESCE(hem.type_fkid, 0) AS TYPE_FKID,
		COALESCE(hem.max_leave, 0) AS MAX_LEAVE,
		hem.name AS NAME,
		COALESCE(typ.type_name, '') AS TYPE_NAME,
		hem.date_join,
		hem.jabatan_fkid,
		COALESCE(ej.name, '') AS jabatan_name
		FROM hrm_employee AS hem
		LEFT JOIN hrm_employee_type AS typ ON typ.type_id = hem.type_fkid
		LEFT JOIN employees_jabatan AS ej ON ej.jabatan_id = hem.jabatan_fkid
		WHERE hem.nik = ? AND hem.admin_fkid = ? AND hem.data_status = 'on'`

	var employee domain.Employee
	err := m.Query(query, nik, adminID).Model(&employee)
	if err != nil || employee.HrmEmployeeID == 0 {
		return nil, err
	}
	return &employee, nil
}

func (m *mySQLEmployeeRepository) FindEmployeeTypeByName(typeName string, adminID int) (*domain.HrmMasterType, error) {
	query := "SELECT type_id, admin_fkid, type_name, type_hours, rest_hours FROM hrm_employee_type WHERE type_name = ? AND admin_fkid = ?"

	var employeeType domain.HrmMasterType
	err := m.Query(query, typeName, adminID).Model(&employeeType)
	if err != nil || employeeType.TypeID == 0 {
		return nil, err
	}
	return &employeeType, nil
}

func (m *mySQLEmployeeRepository) FindJabatanByName(jabatanName string) (*domain.EmployeesJabatan, error) {
	query := "SELECT jabatan_id, name, admin_fkid FROM employees_jabatan WHERE name = ?"

	var jabatan domain.EmployeesJabatan
	err := m.Query(query, jabatanName).Model(&jabatan)
	if err != nil || jabatan.JabatanID == 0 {
		return nil, err
	}
	return &jabatan, nil
}

func (m *mySQLEmployeeRepository) FindOutletByName(outletName string, adminID int) (*domain.Outlet, error) {
	query := "SELECT outlet_id, name, address, admin_fkid FROM outlets WHERE name = ? AND admin_fkid = ?"

	var outlet domain.Outlet
	err := m.Query(query, outletName, adminID).Model(&outlet)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil // Outlet not found
		}
		return nil, err
	}
	return &outlet, nil
}

func (m *mySQLEmployeeRepository) FindAccountByEmail(email string) (*domain.Account, error) {
	query := "SELECT id, name, phone, email, password, created_at, updated_at, is_active FROM accounts WHERE email = ?"

	var account domain.Account
	err := m.Query(query, email).Model(&account)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil // Account not found
		}
		return nil, err
	}
	return &account, nil
}

func (m *mySQLEmployeeRepository) CreateAccount(account domain.Account) (int64, error) {
	accountData := map[string]any{
		"name":       account.Name,
		"phone":      account.Phone,
		"email":      account.Email,
		"password":   account.Password,
		"created_at": time.Now().UnixMilli(),
		"updated_at": time.Now().UnixMilli(),
		"is_active":  1,
	}

	_, accountID, err := m.InsertGetLastID("accounts", accountData)
	if err != nil {
		log.IfError(err)
		return 0, err
	}

	return accountID, nil
}

func (m *mySQLEmployeeRepository) UpsertEmployee(employee domain.EmployeeData, isUpdate bool) (int64, error) {
	log.Info("Upserting employee: %v, isUpdate: %v", cast.ToString(employee), isUpdate)
	var hrmEmployeeID int64
	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Prepare data for hrm_employee table
		hrmEmployeeTableMap := map[string]any{
			"admin_fkid":       employee.AdminId,
			"nik":              employee.Nik,
			"type_fkid":        employee.TypeFkid,
			"max_leave":        employee.MaxLeave,
			"employee_sallary": employee.EmployeeSallary,
			"npwp":             employee.Npwp,
			"data_modified":    time.Now().UnixMilli(),
			"name":             employee.Name,
			"date_join":        employee.JoinDate,
			"jabatan_fkid":     employee.JabatanId,
			"email":            employee.Email,
			"phone":            employee.Phone,
			"address":          employee.Address,
			// "account_id":       employee.AccountId,
			"data_status": "on",
		}

		if employee.OutletIds != "" {
			hrmEmployeeTableMap["outlet_fkid"] = employee.OutletIds
		}

		if isUpdate {
			// Update existing employee
			whereClause := map[string]any{
				"nik":        employee.Nik,
				"admin_fkid": employee.AdminId,
			}
			_, err := m.Updates("hrm_employee", hrmEmployeeTableMap, whereClause)
			if err != nil {
				return err
			}

			// Get the hrm_employee_id for the updated record
			var existingEmployee domain.Employee
			err = m.Query("SELECT hrm_employee_id AS HEMP_ID FROM hrm_employee WHERE nik = ? AND admin_fkid = ?",
				employee.Nik, employee.AdminId).Model(&existingEmployee)
			if err != nil {
				return err
			}
			hrmEmployeeID = int64(existingEmployee.HrmEmployeeID)
			log.Info("updated hrm_employee, nik: %v, hrm_employee_id: %v", employee.Nik, hrmEmployeeID)
		} else {
			// Insert new employee
			hrmEmployeeTableMap["data_created"] = time.Now().UnixMilli()

			result := tx.Insert("hrm_employee", hrmEmployeeTableMap)
			hrmEmployeeID, _ = result.LastInsertId()
			log.Info("inserted hrm_employee: %v", hrmEmployeeID)
		}

		return nil
	})

	return hrmEmployeeID, err
}

func (m *mySQLEmployeeRepository) DeleteAdditionalInfo(hrmEmployeeID int) error {
	_, err := m.Deletes("hrm_additional_employee_info", map[string]any{
		"hrm_employee_fkid": hrmEmployeeID,
	})
	return err
}

// Batch Excel import methods

func (m *mySQLEmployeeRepository) FindEmployeeTypesByNames(typeNames []string, adminID int) ([]*domain.HrmMasterType, error) {
	if len(typeNames) == 0 {
		return []*domain.HrmMasterType{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(typeNames))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	query := fmt.Sprintf("SELECT type_id, admin_fkid, type_name, type_hours, rest_hours FROM hrm_employee_type WHERE type_name IN (%s) AND admin_fkid = ?", placeholders)

	// Convert typeNames to []any and add adminID
	args := make([]any, len(typeNames)+1)
	for i, typeName := range typeNames {
		args[i] = typeName
	}
	args[len(typeNames)] = adminID

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		return nil, err
	}

	var employeeTypes []*domain.HrmMasterType
	for _, result := range results {
		empType := &domain.HrmMasterType{
			TypeID:    cast.ToInt(result["type_id"]),
			AdminFkid: cast.ToInt(result["admin_fkid"]),
			TypeName:  cast.ToString(result["type_name"]),
			TypeHours: cast.ToInt(result["type_hours"]),
			RestHours: cast.ToInt(result["rest_hours"]),
		}
		employeeTypes = append(employeeTypes, empType)
	}

	return employeeTypes, nil
}

func (m *mySQLEmployeeRepository) FindJabatansByNames(jabatanNames []string) ([]*domain.EmployeesJabatan, error) {
	if len(jabatanNames) == 0 {
		return []*domain.EmployeesJabatan{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(jabatanNames))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	query := fmt.Sprintf("SELECT jabatan_id, name, admin_fkid FROM employees_jabatan WHERE name IN (%s)", placeholders)

	// Convert jabatanNames to []any
	args := make([]any, len(jabatanNames))
	for i, jabatanName := range jabatanNames {
		args[i] = jabatanName
	}

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		return nil, err
	}

	var jabatans []*domain.EmployeesJabatan
	for _, result := range results {
		jabatan := &domain.EmployeesJabatan{
			JabatanID: cast.ToInt(result["jabatan_id"]),
			Name:      cast.ToString(result["name"]),
			AdminFkid: cast.ToInt(result["admin_fkid"]),
		}
		jabatans = append(jabatans, jabatan)
	}

	return jabatans, nil
}

func (m *mySQLEmployeeRepository) FindOutletsByNames(outletNames []string, adminID int) ([]*domain.Outlet, error) {
	if len(outletNames) == 0 {
		return []*domain.Outlet{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(outletNames))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	query := fmt.Sprintf("SELECT outlet_id, name, address, admin_fkid FROM outlets WHERE name IN (%s) AND admin_fkid = ?", placeholders)

	// Convert outletNames to []any and add adminID
	args := make([]any, len(outletNames)+1)
	for i, outletName := range outletNames {
		args[i] = outletName
	}
	args[len(outletNames)] = adminID

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		return nil, err
	}

	var outlets []*domain.Outlet
	for _, result := range results {
		outlet := &domain.Outlet{
			OutletID:  cast.ToInt(result["outlet_id"]),
			Name:      cast.ToString(result["name"]),
			Address:   cast.ToString(result["address"]),
			AdminFkid: cast.ToInt(result["admin_fkid"]),
		}
		outlets = append(outlets, outlet)
	}

	return outlets, nil
}

func (m *mySQLEmployeeRepository) FindEmployeesByNIKs(niks []string, adminID int) ([]*domain.Employee, error) {
	if len(niks) == 0 {
		return []*domain.Employee{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(niks))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	query := fmt.Sprintf(`SELECT DISTINCT
		COALESCE(hem.hrm_employee_id, 0) AS HEMP_ID,
		COALESCE(hem.nik, '') AS NIK,
		COALESCE(hem.hrm_employee_id, 0) AS ID,
		COALESCE(hem.employee_sallary, 0) AS SALLARY,
		COALESCE(hem.type_fkid, 0) AS TYPE_FKID,
		COALESCE(hem.max_leave, 0) AS MAX_LEAVE,
		hem.name AS NAME,
		COALESCE(typ.type_name, '') AS TYPE_NAME,
		hem.date_join,
		hem.jabatan_fkid,
		COALESCE(ej.name, '') AS jabatan_name
		FROM hrm_employee AS hem
		LEFT JOIN hrm_employee_type AS typ ON typ.type_id = hem.type_fkid
		LEFT JOIN employees_jabatan AS ej ON ej.jabatan_id = hem.jabatan_fkid
		WHERE hem.nik IN (%s) AND hem.admin_fkid = ? AND hem.data_status = 'on'`, placeholders)

	// Convert niks to []any and add adminID
	args := make([]any, len(niks)+1)
	for i, nik := range niks {
		args[i] = nik
	}
	args[len(niks)] = adminID

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		return nil, err
	}

	var employees []*domain.Employee
	for _, result := range results {
		employee := &domain.Employee{
			HrmEmployeeID:   cast.ToInt(result["HEMP_ID"]),
			Nik:             cast.ToString(result["NIK"]),
			HrEmployeeID:    cast.ToInt(result["ID"]),
			EmployeeSallary: cast.ToInt(result["SALLARY"]),
			TypeFkid:        cast.ToInt(result["TYPE_FKID"]),
			MaxLeave:        cast.ToInt(result["MAX_LEAVE"]),
			Name:            cast.ToString(result["NAME"]),
			TypeName:        cast.ToString(result["TYPE_NAME"]),
			DateJoin:        cast.ToInt64(result["date_join"]),
			JabatanFkid:     cast.ToInt(result["jabatan_fkid"]),
			JabatanName:     cast.ToString(result["jabatan_name"]),
		}
		employees = append(employees, employee)
	}

	return employees, nil
}

func (m *mySQLEmployeeRepository) FindAccountsByEmails(emails []string) ([]*domain.Account, error) {
	if len(emails) == 0 {
		return []*domain.Account{}, nil
	}

	// Build placeholders for IN clause
	placeholders := strings.Repeat("?,", len(emails))
	if len(placeholders) > 0 {
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
	}

	query := fmt.Sprintf("SELECT id, name, phone, email, password, created_at, updated_at, is_active FROM accounts WHERE email IN (%s)", placeholders)

	// Convert emails to []any
	args := make([]any, len(emails))
	for i, email := range emails {
		args[i] = email
	}

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		return nil, err
	}

	var accounts []*domain.Account
	for _, result := range results {
		account := &domain.Account{
			ID:        cast.ToInt64(result["id"]),
			Name:      cast.ToString(result["name"]),
			Phone:     cast.ToString(result["phone"]),
			Email:     cast.ToString(result["email"]),
			Password:  cast.ToString(result["password"]),
			CreatedAt: cast.ToInt64(result["created_at"]),
			UpdatedAt: cast.ToInt64(result["updated_at"]),
			IsActive:  cast.ToInt(result["is_active"]) == 1,
		}
		accounts = append(accounts, account)
	}

	return accounts, nil
}

// BatchUpsertEmployees performs batch insert/update of employees within a transaction
func (m *mySQLEmployeeRepository) BatchUpsertEmployees(processedEmployees []domain.ProcessedEmployeeData, adminID int) (*domain.ExcelImportSummary, error) {
	log.Info("Starting batch upsert of %d employees", len(processedEmployees))

	summary := &domain.ExcelImportSummary{}

	// Use transaction to ensure all-or-nothing behavior
	err := m.WithTransaction(func(tx mysql.Transaction) error {
		// Step 1: Create new accounts in batch
		// var newAccounts []map[string]any
		// accountEmailToIndex := make(map[string]int)

		// for i, processed := range processedEmployees {
		// 	if processed.IsNewAccount {
		// 		// Generate a simple default password hash
		// 		defaultPassword := "password123"
		// 		hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(defaultPassword), bcrypt.DefaultCost)

		// 		newAccount := map[string]any{
		// 			"name":       processed.Name,
		// 			"phone":      processed.Phone,
		// 			"email":      processed.Email,
		// 			"password":   string(hashedPassword),
		// 			"created_at": time.Now().UnixMilli(),
		// 			"updated_at": time.Now().UnixMilli(),
		// 			"is_active":  1,
		// 		}
		// 		newAccounts = append(newAccounts, newAccount)
		// 		accountEmailToIndex[processed.Email] = i
		// 	}
		// }

		// Batch create accounts
		// if len(newAccounts) > 0 {
		// 	result := tx.BulkInsert("accounts", newAccounts)

		// 	// Update processed employees with new account IDs
		// 	if result != nil {
		// 		firstID, err := result.LastInsertId()
		// 		if err == nil {
		// 			for i := range newAccounts {
		// 				email := cast.ToString(newAccounts[i]["email"])
		// 				if empIndex, exists := accountEmailToIndex[email]; exists {
		// 					processedEmployees[empIndex].AccountID = firstID + int64(i)
		// 				}
		// 			}
		// 		}
		// 	}
		// }

		// Step 2: Prepare employee data for batch operations
		var newEmployees []map[string]any
		var updateEmployees []map[string]any

		for _, processed := range processedEmployees {
			employeeMap := map[string]any{
				"admin_fkid":       adminID,
				"nik":              processed.NIK,
				"type_fkid":        processed.TypeID,
				"max_leave":        processed.MaxLeave,
				"employee_sallary": processed.SalaryInt,
				"npwp":             processed.NPWP,
				"data_created":     time.Now().UnixMilli(),
				"data_modified":    time.Now().UnixMilli(),
				"data_status":      "on",
				"name":             processed.Name,
				"date_join":        processed.DateJoinUnix,
				"jabatan_fkid":     processed.JabatanID,
				"email":            processed.Email,
				"phone":            processed.Phone,
				"address":          processed.Address,
			}

			if processed.OutletID > 0 {
				employeeMap["outlet_fkid"] = processed.OutletID
			}

			if processed.IsNewEmployee {
				newEmployees = append(newEmployees, employeeMap)
				summary.NewEmployees++
			} else {
				updateEmployees = append(updateEmployees, employeeMap)
				summary.UpdatedEmployees++
			}

			if processed.IsNewAccount {
				summary.NewAccounts++
			} else {
				summary.ExistingAccounts++
			}
		}

		// Step 3: Batch insert new employees
		if len(newEmployees) > 0 {
			tx.BulkInsert("hrm_employee", newEmployees)
			log.Info("Batch inserted %d new employees", len(newEmployees))
		}

		// Step 4: Batch update existing employees
		if len(updateEmployees) > 0 {
			err := tx.BatchUpdate("hrm_employee", updateEmployees, "nik")
			if err != nil {
				return fmt.Errorf("failed to batch update employees: %v", err)
			}
			log.Info("Batch updated %d existing employees", len(updateEmployees))
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return summary, nil
}
